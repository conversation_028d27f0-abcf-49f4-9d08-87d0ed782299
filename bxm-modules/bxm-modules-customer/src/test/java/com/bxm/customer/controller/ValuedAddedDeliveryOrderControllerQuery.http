### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的query方法
### 基于DeliveryOrderQuery参数完善的真实测试场景
###
### 字段说明：
### - deliveryOrderNo: 交付单编号
### - customerName: 客户企业名称（模糊匹配）
### - creditCode: 统一社会信用代码
### - taxNo: 税号
### - taxpayerType: 纳税性质 (1-小规模纳税人, 2-一般纳税人)
### - valueAddedItemTypeId: 增值事项类型ID
### - accountingPeriodStart/End: 账期开始/结束（YYYYMM）
### - status: 交付状态
### - initiateDeptId: 发起部门ID
### - businessDeptId: 业务部门ID
### - businessTopDeptId: 顶级业务部门ID
### - createUid: 发起人用户ID
### - ddlStart/End: DDL日期范围（yyyy-MM-dd）
### - pageNum: 当前页
### - pageSize: 每页显示数量

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImNjOGJkNTNkLTU2ZDctNDc0My04OTc2LTM0NTc0ZTEwZGNmZSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.s_woe6XxCNDaTuIGEVKfopxsfDGR3zPzcJy4isYNDlXQfvKOkG8BAXPfGEXnmA_Rv-WEMxfbZzAtaUeBXfmeGA
### ========================================
### 1. 测试查询所有增值交付单（无条件分页查询）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 2. 测试根据客户名称模糊查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?customerName=测试企业&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 3. 测试根据交付单编号精确查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?deliveryOrderNo=VAD2508051430001A1C&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 4. 测试根据纳税性质查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?taxpayerType=1&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 5. 测试根据增值事项类型查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?valueAddedItemTypeId=2&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 6. 测试根据交付状态查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?status=DRAFT&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 7. 测试根据统一社会信用代码查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?creditCode=91350105MA2Y9DXW8L&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 8. 测试根据账期范围查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?accountingPeriodStart=202501&accountingPeriodEnd=202512&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 9. 测试根据DDL日期范围查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?ddlStart=2025-01-01&ddlEnd=2025-12-31&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 10. 测试根据发起部门ID查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?initiateDeptId=1&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 11. 测试根据业务部门ID查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?businessDeptId=101&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 12. 测试根据发起人用户ID查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?createUid=1&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 13. 测试组合条件查询（客户名称+纳税性质+状态）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?customerName=测试&taxpayerType=1&status=DRAFT&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 14. 测试组合条件查询（增值事项+账期范围）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?valueAddedItemTypeId=2&accountingPeriodStart=202501&accountingPeriodEnd=202512&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 15. 测试组合条件查询（部门+DDL范围）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?businessDeptId=101&ddlStart=2025-06-01&ddlEnd=2025-12-31&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 16. 测试复杂组合条件查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?customerName=企业&taxpayerType=2&valueAddedItemTypeId=1&status=DRAFT&initiateDeptId=1&pageNum=1&pageSize=5
Authorization: {{authorization}}

### ========================================
### 17. 测试大页码查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?pageNum=5&pageSize=20
Authorization: {{authorization}}

### ========================================
### 18. 测试空条件查询（验证默认排序）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query
Authorization: {{authorization}}

### ========================================
### 19. 测试根据税号查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?taxNo=91350105MA2Y9DXW8L&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 20. 测试边界条件 - 账期开始时间查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?accountingPeriodStart=202501&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 21. 测试边界条件 - 账期结束时间查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?accountingPeriodEnd=202512&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 22. 测试边界条件 - DDL开始日期查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?ddlStart=2025-06-01&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 23. 测试边界条件 - DDL结束日期查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?ddlEnd=2025-12-31&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 24. 测试顶级业务部门查询
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?businessTopDeptId=100&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 25. 测试所有条件组合查询（完整测试）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?customerName=测试&creditCode=91350105MA2Y9DXW8L&taxpayerType=1&valueAddedItemTypeId=2&accountingPeriodStart=202501&accountingPeriodEnd=202512&status=DRAFT&initiateDeptId=1&businessDeptId=101&businessTopDeptId=100&createUid=1&ddlStart=2025-01-01&ddlEnd=2025-12-31&pageNum=1&pageSize=10
Authorization: {{authorization}}
