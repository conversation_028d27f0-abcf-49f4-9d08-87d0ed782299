### ValueAddedEmployeeController HTTP测试文件
### 增值员工信息管理接口核心测试
### 支持四种业务类型：1-社医保，2-个税明细，3-国税账号，4-个税账号
###
### 核心功能测试：
### 1-4: 四种业务类型的 upsert 操作
### 5-7: 更新和验证测试
### 8-11: 查询和Excel功能测试
### 12-15: 批量操作和导出测试

### 环境变量定义
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjFkNWI3OWQwLThhZWEtNDU1NS1hMWNlLWE5ZGVmNzk4ZWNiNSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.eYd0eC7jKwkVj7mlpWsax3KHffnW6rUDVYFC2gt10DeW1VBI11qtuLjaVOesnZzyZwA0M0KHbaygW3dfGKn_Nw

### ========================================
### 1. 社医保业务 - upsert 操作
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-001",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "张三",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 8000.00,
  "providentFundPersonal": 800.00,
  "socialInsurance": {
    "yangLao": true,
    "shiYe": true,
    "gongShang": true,
    "yiLiao": true,
    "shengYu": true,
    "qiTa": false
  },
  "status": 1,
  "remark": "社医保测试"
}

### ========================================
### 2. 个税明细业务 - upsert 操作
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-002",
  "bizType": 2,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "李四",
  "idNumber": "110101199002022345",
  "mobile": "13800138002",
  "grossSalary": 12000.00,
  "providentFundPersonal": 1200.00,
  "status": 1,
  "socialInsurance": {
    "yangLao": true,
    "shiYe": true,
    "gongShang": true,
    "yiLiao": true,
    "shengYu": true,
    "qiTa": false
  },
  "extendInfo": "{\"housing_fund_personal_amount\":\"1200.00\",\"other\":\"无其他费用\"}",
  "remark": "个税明细测试"
}

### ========================================
### 3. 国税账号业务 - upsert 操作
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-003",
  "bizType": 3,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "王五",
  "idNumber": "110101199003033456",
  "mobile": "13800138003",
  "taxNumber": "91110000123456789X",
  "queryPassword": "password123",
  "status": 1,
  "extendInfo": "{\"certificationDate\":\"2025-01-28\",\"customField\":\"会计实名认证\"}",
  "remark": "国税账号测试"
}

### ========================================
### 4. 个税账号业务 - upsert 操作
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-004",
  "bizType": 4,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "赵六",
  "idNumber": "110101199004044567",
  "mobile": "13800138004",
  "taxNumber": "91110000555666777Z",
  "queryPassword": "password789",
  "loginMethod": "手机号+密码",
  "realNameAgent": "张会计",
  "status": 1,
  "remark": "个税账号测试"
}

### ========================================
### 5. 更新操作测试（包含ID字段）
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "DO-2025-001",
  "bizType": 1,
  "entryType": 2,
  "operationType": 2,
  "employeeName": "张三（已更新）",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 8500.00,
  "providentFundPersonal": 850.00,
  "socialInsurance": {
    "yangLao": true,
    "shiYe": true,
    "gongShang": true,
    "yiLiao": true,
    "shengYu": true,
    "qiTa": false
  },
  "status": 2,
  "remark": "更新操作测试"
}

### ========================================
### 6. 必填字段验证测试（应失败）
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "测试用户"
}

### ========================================
### 7. 业务类型验证测试（应失败）
### ========================================
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-999",
  "bizType": 5,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "业务类型错误测试",
  "idNumber": "110101199001011234",
  "mobile": "13800138001",
  "grossSalary": 8000.00
}

### ========================================
### 8. 分页查询测试 - 社医保
### ========================================
GET {{baseUrl}}/valueAddedEmployee/query?bizType=1&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 9. 分页查询测试 - 个税明细
### ========================================
GET {{baseUrl}}/valueAddedEmployee/query?bizType=2&deliveryOrderNo=DO-2025-002&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 10. 导出Excel模板测试 - 社医保
### ========================================
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=1
Authorization: {{authorization}}

### ========================================
### 11. 导出Excel模板测试 - 个税明细
### ========================================
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=2
Authorization: {{authorization}}

### ========================================
### 12. 批量上传测试 - 社医保
### ========================================
POST {{baseUrl}}/valueAddedEmployee/batchSaveValueAddedEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-001
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

社医保批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="test.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 13. 批量上传测试 - 个税明细
### ========================================
POST {{baseUrl}}/valueAddedEmployee/batchSaveValueAddedEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-002
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

2
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

个税明细批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="test.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 14. 导出员工明细测试
### ========================================
GET {{baseUrl}}/valueAddedEmployee/exportDetail?bizType=1&deliveryOrderNo=DO-2025-001
Authorization: {{authorization}}

### ========================================
### 15. 根据条件查询员工信息测试
### ========================================
GET {{baseUrl}}/valueAddedEmployee/getByDeliveryOrderAndIdNumber?deliveryOrderNo=DO-2025-001&idNumber=110101199001011234&bizType=1
Authorization: {{authorization}}
