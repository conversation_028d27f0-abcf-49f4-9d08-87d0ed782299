package com.bxm.customer.service.strategy.valueadded;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractValueAddedEmployeeUpsertStrategy;
import com.bxm.customer.utils.IdNumberValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 个税明细业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PersonalTaxUpsertStrategy extends AbstractValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.PERSONAL_TAX.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 调用父类通用校验
        validateUniquenessInDeliveryOrder(employee);
        validateOperationTypeConditions(employee);

        // 个税明细业务强制校验身份证号
        IdNumberValidationUtil.validateAndThrow(employee.getIdNumber(), "身份证号");

        // 验证操作类型是否适用于个税明细业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.PERSONAL_TAX, employee.getOperationType())) {
            throw new IllegalArgumentException("个税明细业务不支持的操作类型: " + employee.getOperationType());
        }

        // 验证应发工资（个税明细业务必须有工资信息）
        if (employee.getGrossSalary() == null || employee.getGrossSalary().doubleValue() <= 0) {
            throw new IllegalArgumentException("个税明细业务应发工资不能为空且必须为正数");
        }

        // 校验申报险种前置条件：方式为增员或更正时必填，至少选中一个
        if (isAddOrCorrectionOperation(employee.getOperationType())) {
            if (employee.getSocialInsurance() == null) {
                throw new IllegalArgumentException("方式为增员或更正时，申报险种不能为空");
            }

            // 检查是否至少选中一个险种
            if (!employee.getSocialInsurance().isValid()) {
                String validationMessage = employee.getSocialInsurance().getValidationMessage();
                if (validationMessage != null) {
                    throw new IllegalArgumentException(validationMessage);
                } else {
                    throw new IllegalArgumentException("方式为增员或更正时，申报险种至少需要选中一个");
                }
            }
        }
    }

    @Override
    public void preprocessEmployee(ValueAddedEmployee employee) {
        // 设置业务类型
        employee.setBizType(ValueAddedBizType.PERSONAL_TAX.getCode());

        // 清理其他业务类型专用字段
        employee.setTaxNumber(null);
        employee.setQueryPassword(null);
        // 个税明细业务保留 socialInsurance 字段，用于存储社保信息

        // 标准化身份证号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getIdNumber())) {
            employee.setIdNumber(employee.getIdNumber().trim().toUpperCase());
        }


    }

    @Override
    public void postprocessEmployee(ValueAddedEmployee employee, boolean isUpdate) {
        String operation = isUpdate ? "updated" : "created";
        log.info("Personal tax employee {} successfully: ID={}, Name={}, Operation={}, Salary={}", operation, employee.getId(), employee.getEmployeeName(), employee.getOperationType(), employee.getGrossSalary());

        // 个税明细业务的后处理逻辑
        // 例如：触发个税计算、发送通知等
    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 个税明细业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.PERSONAL_TAX.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    public ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新基础信息
        existing.setEmployeeName(newEmployee.getEmployeeName());
        existing.setMobile(newEmployee.getMobile());
        existing.setOperationType(newEmployee.getOperationType());
        existing.setEntryType(newEmployee.getEntryType());

        // 更新个税明细特定字段
        if (newEmployee.getGrossSalary() != null) {
            existing.setGrossSalary(newEmployee.getGrossSalary());
        }
        if (newEmployee.getProvidentFundPersonal() != null) {
            existing.setProvidentFundPersonal(newEmployee.getProvidentFundPersonal());
        }
        if (newEmployee.getSocialInsurance() != null) {
            existing.setSocialInsurance(newEmployee.getSocialInsurance());
        }
        if (StringUtils.isNotEmpty(newEmployee.getExtendInfo())) {
            existing.setExtendInfo(newEmployee.getExtendInfo());
        }
        if (StringUtils.isNotEmpty(newEmployee.getRemark())) {
            existing.setRemark(newEmployee.getRemark());
        }

        return existing;
    }


}
