package com.bxm.customer.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 交付单校验工具类
 * 
 * 提供交付单内员工信息的唯一性校验和操作类型相关的校验方法
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Component
public class DeliveryOrderValidationUtil {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    /**
     * 检查同一交付单内身份证号的唯一性
     * 
     * @param deliveryOrderNo 交付单编号
     * @param idNumber 身份证号
     * @param bizType 业务类型
     * @param excludeId 排除的员工ID（用于更新时排除自己）
     * @return 是否唯一（true表示唯一，false表示重复）
     */
    public boolean checkIdNumberUniquenessInDeliveryOrder(String deliveryOrderNo, String idNumber, 
                                                         Integer bizType, Long excludeId) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(idNumber)) {
            return true; // 空值不参与唯一性校验
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getIdNumber, idNumber.trim())
                .eq(ValueAddedEmployee::getBizType, bizType);
        
        // 更新时排除自己
        if (excludeId != null) {
            queryWrapper.ne(ValueAddedEmployee::getId, excludeId);
        }

        List<ValueAddedEmployee> existingEmployees = valueAddedEmployeeMapper.selectList(queryWrapper);
        boolean isUnique = existingEmployees.isEmpty();
        
        if (!isUnique) {
            log.warn("Duplicate ID number found in delivery order: deliveryOrderNo={}, idNumber={}, bizType={}", 
                    deliveryOrderNo, idNumber, bizType);
        }
        
        return isUnique;
    }

    /**
     * 检查同一交付单内手机号的唯一性
     * 
     * @param deliveryOrderNo 交付单编号
     * @param mobile 手机号
     * @param bizType 业务类型
     * @param excludeId 排除的员工ID（用于更新时排除自己）
     * @return 是否唯一（true表示唯一，false表示重复）
     */
    public boolean checkMobileUniquenessInDeliveryOrder(String deliveryOrderNo, String mobile, 
                                                       Integer bizType, Long excludeId) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(mobile)) {
            return true; // 空值不参与唯一性校验
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getMobile, mobile.trim())
                .eq(ValueAddedEmployee::getBizType, bizType);
        
        // 更新时排除自己
        if (excludeId != null) {
            queryWrapper.ne(ValueAddedEmployee::getId, excludeId);
        }

        List<ValueAddedEmployee> existingEmployees = valueAddedEmployeeMapper.selectList(queryWrapper);
        boolean isUnique = existingEmployees.isEmpty();
        
        if (!isUnique) {
            log.warn("Duplicate mobile found in delivery order: deliveryOrderNo={}, mobile={}, bizType={}", 
                    deliveryOrderNo, mobile, bizType);
        }
        
        return isUnique;
    }

    /**
     * 判断操作类型是否为增员或更正
     *
     * @param operationType 操作类型
     * @return 是否为增员或更正操作
     */
    public static boolean isAddOrCorrectionOperation(Integer operationType) {
        if (operationType == null) {
            return false;
        }

        // 根据ValueAddedOperationType枚举定义：
        // 1-提醒，2-更正，3-减员
        // 根据业务需求，"增员"操作对应枚举中的"提醒"(1)，"更正"操作对应枚举中的"更正"(2)
        // 当操作类型为提醒或更正时，需要校验申报基数和申报险种
        return operationType.equals(ValueAddedOperationType.REMIND.getCode()) ||
               operationType.equals(ValueAddedOperationType.CORRECTION.getCode());
    }

    /**
     * 校验申报基数的前置条件
     * 
     * @param operationType 操作类型
     * @param socialInsuranceBase 申报基数
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateSocialInsuranceBaseCondition(Integer operationType, Object socialInsuranceBase) {
        if (isAddOrCorrectionOperation(operationType)) {
            if (socialInsuranceBase == null) {
                throw new IllegalArgumentException("方式为增员或更正时，申报基数不能为空");
            }
        }
    }
}
