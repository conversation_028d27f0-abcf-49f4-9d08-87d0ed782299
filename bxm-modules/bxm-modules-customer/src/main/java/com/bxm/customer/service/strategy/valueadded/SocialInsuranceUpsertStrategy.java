package com.bxm.customer.service.strategy.valueadded;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractValueAddedEmployeeUpsertStrategy;
import com.bxm.customer.utils.IdNumberValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 社医保业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class SocialInsuranceUpsertStrategy extends AbstractValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.SOCIAL_INSURANCE.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 社医保业务强制校验身份证号
        IdNumberValidationUtil.validateAndThrow(employee.getIdNumber(), "身份证号");

        // 验证操作类型是否适用于社医保业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.SOCIAL_INSURANCE, employee.getOperationType())) {
            throw new IllegalArgumentException("社医保业务不支持的操作类型: " + employee.getOperationType());
        }

        // 验证应发工资（社医保业务通常需要工资信息）
        if (employee.getGrossSalary() == null || employee.getGrossSalary().doubleValue() < 0) {
            throw new IllegalArgumentException("社医保业务应发工资不能为空且必须为非负数");
        }
    }

    @Override
    protected void doPreprocess(ValueAddedEmployee employee) {
        // 如果没有提供社保信息，设置默认值
        if (employee.getSocialInsurance() == null) {
            employee.setSocialInsurance(getDefaultSocialInsurance());
        }
    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 社医保业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.SOCIAL_INSURANCE.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    protected void doMerge(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新社医保特定字段
        if (newEmployee.getGrossSalary() != null) {
            existing.setGrossSalary(newEmployee.getGrossSalary());
        }
        if (newEmployee.getProvidentFundPersonal() != null) {
            existing.setProvidentFundPersonal(newEmployee.getProvidentFundPersonal());
        }
        if (newEmployee.getSocialInsurance() != null) {
            existing.setSocialInsurance(newEmployee.getSocialInsurance());
        }
    }



    /**
     * 获取默认的社保信息
     *
     * @return 默认社保信息对象
     */
    private SocialInsuranceVO getDefaultSocialInsurance() {
        return SocialInsuranceVO.createDefault();
    }
}
