package com.bxm.customer.validation;

import com.bxm.common.core.utils.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 身份证号验证器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class IdNumberValidator implements ConstraintValidator<IdNumber, String> {

    private boolean allowEmpty;

    @Override
    public void initialize(IdNumber constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && StringUtils.isEmpty(value)) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && StringUtils.isEmpty(value)) {
            return false;
        }

        // 去除空格
        value = value.trim();

        // 18位身份证号验证
        if (value.length() == 18) {
            return isValid18IdNumber(value);
        }

        // 15位身份证号验证（老版本）
        if (value.length() == 15) {
            return isValid15IdNumber(value);
        }

        return false;
    }

    /**
     * 验证18位身份证号
     */
    private boolean isValid18IdNumber(String idNumber) {
        // 前17位必须是数字，最后一位可以是数字或X
        String first17 = idNumber.substring(0, 17);
        String last1 = idNumber.substring(17);

        if (!first17.matches("\\d{17}")) {
            return false;
        }

        if (!last1.matches("[0-9Xx]")) {
            return false;
        }

        // 可以在这里添加更严格的校验，如校验码验证
        return true;
    }

    /**
     * 验证15位身份证号
     */
    private boolean isValid15IdNumber(String idNumber) {
        return idNumber.matches("\\d{15}");
    }
}
